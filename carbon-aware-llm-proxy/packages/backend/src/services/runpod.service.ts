import axios, { AxiosInstance } from "axios";
import { logger } from "../utils/logger";
import { databaseService } from "./database.service";
import {
  RunPodDeployment,
  DeploymentStatus,
} from "../entities/RunPodDeployment";
import { RunPodInstance, InstanceStatus } from "../entities/RunPodInstance";
import {
  MODEL_CONFIGS,
  GPU_TYPES,
  RUNPOD_REGIONS,
  VLLM_STARTUP_COMMAND,
  HEALTH_CHECK_CONFIG,
  ModelConfig,
  GpuType,
  RunPodRegion,
} from "../config/runpod.config";

interface RunPodPod {
  id: string;
  name: string;
  runtime: {
    uptimeInSeconds: number;
    ports: Array<{
      ip: string;
      isIpPublic: boolean;
      privatePort: number;
      publicPort: number;
      type: string;
    }>;
  };
  machine: {
    podHostId: string;
    gpuCount: number;
    vcpuCount: number;
    memoryInGb: number;
    diskInGb: number;
  };
  desiredStatus: string;
  lastStatusChange: string;
  costPerHr: number;
}

interface RunPodEndpoint {
  id: string;
  name: string;
  status: string;
  url?: string;
  pods: RunPodPod[];
}

class RunPodService {
  private client: AxiosInstance;
  private apiKey: string;

  constructor() {
    this.apiKey = process.env.RUNPOD_API_KEY || "";
    if (!this.apiKey) {
      logger.warn(
        "RunPod API key not configured. RunPod integration will be disabled.",
      );
    }

    this.client = axios.create({
      baseURL: "https://api.runpod.io/graphql",
      headers: {
        Authorization: `Bearer ${this.apiKey}`,
        "Content-Type": "application/json",
      },
      timeout: 30000,
    });
  }

  /**
   * Deploy a model to RunPod in specified regions
   */
  async deployModel(
    modelId: ModelConfig,
    regions: RunPodRegion[],
    options: {
      gpuType?: GpuType;
      minReplicas?: number;
      maxReplicas?: number;
      autoScaling?: boolean;
    } = {},
  ): Promise<RunPodDeployment[]> {
    const modelConfig = MODEL_CONFIGS[modelId];
    if (!modelConfig) {
      throw new Error(`Unknown model configuration: ${modelId}`);
    }

    const {
      gpuType = "NVIDIA GeForce RTX 4090",
      minReplicas = 1,
      maxReplicas = 3,
      autoScaling = true,
    } = options;

    const deployments: RunPodDeployment[] = [];

    for (const region of regions) {
      try {
        logger.info(`Deploying ${modelId} to region ${region}`);

        const deployment = await this.createDeployment(
          modelId,
          modelConfig,
          region,
          gpuType,
          { minReplicas, maxReplicas, autoScaling },
        );

        deployments.push(deployment);
      } catch (error) {
        logger.error(`Failed to deploy ${modelId} to region ${region}:`, error);
        // Continue with other regions even if one fails
      }
    }

    return deployments;
  }

  /**
   * Create a new deployment in the database and initiate RunPod endpoint creation
   */
  private async createDeployment(
    modelId: ModelConfig,
    modelConfig: (typeof MODEL_CONFIGS)[ModelConfig],
    region: RunPodRegion,
    gpuType: GpuType,
    options: { minReplicas: number; maxReplicas: number; autoScaling: boolean },
  ): Promise<RunPodDeployment> {
    const deploymentRepo = databaseService
      .getDataSource()
      .getRepository(RunPodDeployment);

    // Check if deployment already exists for this model and region
    const existingDeployment = await deploymentRepo.findOne({
      where: { modelId, region },
    });

    if (
      existingDeployment &&
      existingDeployment.status !== DeploymentStatus.FAILED
    ) {
      logger.info(`Deployment already exists for ${modelId} in ${region}`);
      return existingDeployment;
    }

    const regionConfig = RUNPOD_REGIONS[region];
    const gpuConfig = GPU_TYPES[gpuType];

    // Create deployment record
    const deployment = deploymentRepo.create({
      modelId,
      region,
      gpuType,
      status: DeploymentStatus.PENDING,
      minReplicas: options.minReplicas,
      maxReplicas: options.maxReplicas,
      autoScaling: options.autoScaling,
      carbonIntensity: regionConfig.carbonIntensity,
      deploymentCostPerHour: gpuConfig.costPerHour,
      configuration: {
        modelConfig,
        regionConfig,
        gpuConfig,
      },
    });

    const savedDeployment = await deploymentRepo.save(deployment);

    // Initiate RunPod endpoint creation asynchronously
    this.createRunPodEndpoint(savedDeployment).catch((error) => {
      logger.error(
        `Failed to create RunPod endpoint for deployment ${savedDeployment.id}:`,
        error,
      );
      this.updateDeploymentStatus(
        savedDeployment.id,
        DeploymentStatus.FAILED,
        error instanceof Error ? error.message : String(error),
      );
    });

    return savedDeployment;
  }

  /**
   * Create RunPod serverless endpoint
   */
  private async createRunPodEndpoint(
    deployment: RunPodDeployment,
  ): Promise<void> {
    const modelConfig = MODEL_CONFIGS[deployment.modelId as ModelConfig];

    const mutation = `
      mutation {
        saveTemplate(input: {
          containerDiskInGb: ${deployment.containerDiskSize}
          dockerArgs: "${VLLM_STARTUP_COMMAND(modelConfig.modelName, modelConfig).join(" ")}"
          env: [
            {key: "MODEL_NAME", value: "${modelConfig.modelName}"}
            {key: "MAX_MODEL_LEN", value: "${modelConfig.maxSequenceLength}"}
            {key: "TENSOR_PARALLEL_SIZE", value: "${modelConfig.tensorParallelism}"}
          ]
          imageName: "${modelConfig.dockerImage}"
          name: "${deployment.modelId}-${deployment.region}-${Date.now()}"
          ports: "8000/http"
          volumeInGb: ${deployment.volumeSize}
          volumeMountPath: "/workspace"
        }) {
          id
          name
        }
      }
    `;

    try {
      await this.updateDeploymentStatus(
        deployment.id,
        DeploymentStatus.DEPLOYING,
      );

      const response = await this.client.post("", { query: mutation });

      if (response.data.errors) {
        throw new Error(
          `GraphQL errors: ${JSON.stringify(response.data.errors)}`,
        );
      }

      const templateId = response.data.data.saveTemplate.id;

      // Create serverless endpoint
      const endpointMutation = `
        mutation {
          saveEndpoint(input: {
            name: "${deployment.modelId}-${deployment.region}"
            templateId: "${templateId}"
            gpuIds: "${this.getGpuId(deployment.gpuType)}"
            idleTimeout: ${deployment.maxIdleTime}
            scalerType: "QUEUE_DELAY"
            scalerValue: 1
            workersMin: ${deployment.minReplicas}
            workersMax: ${deployment.maxReplicas}
            locations: "${deployment.region}"
          }) {
            id
            name
            url
          }
        }
      `;

      const endpointResponse = await this.client.post("", {
        query: endpointMutation,
      });

      if (endpointResponse.data.errors) {
        throw new Error(
          `Endpoint creation errors: ${JSON.stringify(endpointResponse.data.errors)}`,
        );
      }

      const endpoint = endpointResponse.data.data.saveEndpoint;

      // Update deployment with endpoint information
      const deploymentRepo = databaseService
        .getDataSource()
        .getRepository(RunPodDeployment);
      await deploymentRepo.update(deployment.id, {
        runpodEndpointId: endpoint.id,
        endpointUrl: endpoint.url,
        status: DeploymentStatus.RUNNING,
      });

      logger.info(
        `Successfully created RunPod endpoint for deployment ${deployment.id}`,
      );
    } catch (error) {
      logger.error(`Failed to create RunPod endpoint:`, error);
      await this.updateDeploymentStatus(
        deployment.id,
        DeploymentStatus.FAILED,
        error instanceof Error ? error.message : String(error),
      );
      throw error;
    }
  }

  /**
   * Get GPU ID for RunPod API based on GPU type
   */
  private getGpuId(gpuType: string): string {
    const gpuMap: Record<string, string> = {
      "NVIDIA GeForce RTX 4090": "NVIDIA GeForce RTX 4090",
      "NVIDIA RTX A6000": "NVIDIA RTX A6000",
      "NVIDIA A100 80GB": "NVIDIA A100 80GB PCIe",
    };

    return gpuMap[gpuType] || gpuType;
  }

  /**
   * Update deployment status
   */
  private async updateDeploymentStatus(
    deploymentId: string,
    status: DeploymentStatus,
    errorMessage?: string,
  ): Promise<void> {
    const deploymentRepo = databaseService
      .getDataSource()
      .getRepository(RunPodDeployment);
    const updateData: Partial<RunPodDeployment> = { status };

    if (errorMessage) {
      updateData.errorMessage = errorMessage;
    }

    await deploymentRepo.update(deploymentId, updateData);
  }

  /**
   * Get all active deployments
   */
  async getActiveDeployments(): Promise<RunPodDeployment[]> {
    const deploymentRepo = databaseService
      .getDataSource()
      .getRepository(RunPodDeployment);
    return deploymentRepo.find({
      where: {
        status: DeploymentStatus.RUNNING,
      },
      relations: ["model", "instances"],
    });
  }

  /**
   * Get deployments for a specific model
   */
  async getModelDeployments(modelId: string): Promise<RunPodDeployment[]> {
    const deploymentRepo = databaseService
      .getDataSource()
      .getRepository(RunPodDeployment);
    return deploymentRepo.find({
      where: { modelId },
      relations: ["model", "instances"],
      order: { createdAt: "DESC" },
    });
  }

  /**
   * Health check for all active deployments
   */
  async performHealthChecks(): Promise<void> {
    const activeDeployments = await this.getActiveDeployments();

    for (const deployment of activeDeployments) {
      try {
        await this.checkDeploymentHealth(deployment);
      } catch (error) {
        logger.error(
          `Health check failed for deployment ${deployment.id}:`,
          error,
        );
      }
    }
  }

  /**
   * Check health of a specific deployment
   */
  private async checkDeploymentHealth(
    deployment: RunPodDeployment,
  ): Promise<void> {
    if (!deployment.endpointUrl) {
      logger.warn(`No endpoint URL for deployment ${deployment.id}`);
      return;
    }

    try {
      const healthUrl = `${deployment.endpointUrl}${HEALTH_CHECK_CONFIG.endpoint}`;
      const response = await axios.get(healthUrl, {
        timeout: HEALTH_CHECK_CONFIG.timeoutSeconds * 1000,
      });

      const isHealthy = response.status === 200;

      const deploymentRepo = databaseService
        .getDataSource()
        .getRepository(RunPodDeployment);
      await deploymentRepo.update(deployment.id, {
        healthStatus: isHealthy ? "healthy" : "unhealthy",
        lastHealthCheck: new Date(),
      });

      if (isHealthy) {
        logger.debug(`Health check passed for deployment ${deployment.id}`);
      } else {
        logger.warn(
          `Health check failed for deployment ${deployment.id}: Status ${response.status}`,
        );
      }
    } catch (error) {
      logger.error(
        `Health check error for deployment ${deployment.id}:`,
        error,
      );

      const deploymentRepo = databaseService
        .getDataSource()
        .getRepository(RunPodDeployment);
      await deploymentRepo.update(deployment.id, {
        healthStatus: "unhealthy",
        lastHealthCheck: new Date(),
      });
    }
  }
}

export const runPodService = new RunPodService();
