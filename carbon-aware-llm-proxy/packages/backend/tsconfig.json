{"compilerOptions": {"target": "ES2021", "module": "commonjs", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "useDefineForClassFields": false, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "typeRoots": ["./node_modules/@types", "./src/types"]}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.test.ts"]}