{"name": "@carbon-aware-llm/backend", "version": "0.1.0", "private": true, "description": "Carbon-Aware LLM Proxy Gateway Backend", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint . --ext .ts", "format": "prettier --write \"**/*.ts\"", "test": "jest", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "migration:generate": "npm run typeorm -- migration:generate -d src/config/database.ts", "migration:run": "npm run typeorm -- -d src/config/database.ts migration:run", "migration:revert": "npm run typeorm -- -d src/config/database.ts migration:revert", "migration:create": "npm run typeorm -- migration:create"}, "dependencies": {"axios": "^1.10.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "ioredis": "^5.3.2", "pg": "^8.16.3", "pino": "^9.7.0", "pino-http": "^10.5.0", "pino-pretty": "^13.0.0", "rate-limiter-flexible": "^7.1.1", "redis": "4.7.1", "reflect-metadata": "^0.2.2", "runpod-sdk": "^1.1.2", "typeorm": "^0.3.25", "ws": "^8.18.3", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-rate-limit": "^5.1.3", "@types/express-validator": "^3.0.2", "@types/jest": "^29.5.11", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.10.6", "@types/supertest": "^2.0.15", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "jest": "^29.7.0", "prettier": "^3.1.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^4.9.5"}, "engines": {"node": ">=18.0.0"}}