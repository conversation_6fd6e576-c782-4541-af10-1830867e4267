{"name": "@carbon-aware-llm/frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "tsc": "tsc"}, "dependencies": {"@heroicons/react": "^2.1.1", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "axios": "^1.6.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.3.1", "lucide-react": "^0.525.0", "next": "14.1.0", "next-themes": "^0.2.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.60.0", "recharts": "^2.10.4", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.19.7", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-config-next": "14.1.0", "eslint-config-prettier": "^9.1.0", "postcss": "^8.4.32", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.7", "tailwindcss": "^3.4.0", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}