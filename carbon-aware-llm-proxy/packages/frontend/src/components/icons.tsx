import { Sparkles } from "@/components/ui/icons/sparkles";
import {
  Loader2,
  Send,
  Music,
  HelpCircle,
  AlertCircle,
  Bell,
  BellOff,
  CheckCircle2,
  XCircle,
  PlusCircle,
  MinusCircle,
  AlertOctagon,
  AlertTriangle,
  Info as InfoIcon,
  Check as CheckIcon,
  X,
  ChevronUp,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronsUp,
  ChevronsDown,
  ChevronsLeft,
  ChevronsRight,
  ArrowUp,
  ArrowDown as ArrowDownIcon,
  ArrowLeft,
  ArrowRight,
  ArrowUpRight,
  ArrowDownRight,
  ArrowDownLeft,
  ArrowUpLeft,
  Maximize2,
  Minimize2,
  Move,
  RotateCw,
  RotateCcw,
  ZoomIn,
  ZoomOut,
  Github,
  Twitter,
  Facebook,
  // Removed Instagram, Linkedin, Youtube, Twitch, Slack as they're not available in current lucide-react version
  GitBranch,
  GitCommit,
  GitMerge,
  GitPullRequest,
  GitCompare,
  GitCommit as GitCommitIcon,
  GitMerge as GitMergeIcon,
  GitPullRequest as GitPullRequestIcon,
  GitCompare as GitCompareIcon,
  Loader2 as Spinner,
  Lock,
  Unlock,
  Book as BookIcon,
  Notebook as NotebookIcon,
  Calendar,
  Leaf,
  Battery,
  Cpu,
  Mail,
  CheckCircle,
  Edit,
  Trash2,
  RefreshCw,
  Droplet,
  Zap,
  Activity,
  Download,
  User,
  Settings,
  LogOut,
  Search,
  Home,
  FileText,
  BarChart3,
  Check,
  Info,
  Menu,
  Clock,
  Heart,
  Star,
  ThumbsUp,
  ThumbsDown,
  MessageSquare,
  Copy,
  ExternalLink,
  MoreVertical,
  Save,
  Play,
  Pause,
  Camera,
  Image,
  Folder,
  Upload,
  Cloud,
  Sun,
  Moon,
  Monitor,
  Smartphone,
  Globe,
  Wifi,
  Power,
  BatteryCharging,
  Signal,
  Car,
  MapPin,
  Building,
  ShoppingCart,
  Gift,
  Award,
  Trophy,
  Smile,
  Frown,
  Hand,
  Eye,
  EyeOff,
  Clock as ClockIcon,
  Heart as HeartIcon,
  Star as StarIcon,
  MessageSquare as MessageIcon,
  Share2,
  Filter,
  Plus,
  Minus,
  MoreHorizontal,
  Edit3,
  X as Cancel,
  SkipForward,
  SkipBack,
  Volume2,
  VolumeX,
  Mic,
  MicOff,
  Video,
  VideoOff,
  File,
  FolderOpen,
  DownloadCloud,
  CloudRain,
  CloudSnow,
  Laptop,
  Monitor as Desktop,
  Server,
  Database,
  Bluetooth,
  Usb,
  Cable as Hdmi,
  Printer,
  Rocket,
  Anchor,
  Compass,
  Map,
  MapPin as Location,
  Coffee,
  Pizza,
  Beer,
  Wine,
  ShoppingCart as CartIcon,
  Gift as GiftIcon,
  Award as AwardIcon,
  Trophy as TrophyIcon,
  Crown,
  Diamond,
  Bot as Robot,
  Skull,
  Bone,
} from "lucide-react";

export const Icons = {
  // logo: Sparkles,  // Temporarily commented out as we need to implement a custom Sparkles component
  send: Send,
  loader: Loader2,
  leaf: Leaf,
  battery: Battery,
  cpu: Cpu,
  mail: Mail,
  checkCircle: CheckCircle,
  spinner: Loader2,
  edit: Edit,
  trash: Trash2,
  refreshCw: RefreshCw,
  droplet: Droplet,
  zap: Zap,
  activity: Activity,
  download: Download,
  user: User,
  settings: Settings,
  logOut: LogOut,
  chevronDown: ChevronDown,
  search: Search,
  x: X,
  home: Home,
  fileText: FileText,
  barChart3: BarChart3,
  alertTriangle: AlertTriangle,
  check: Check,
  info: Info,
  menu: Menu,
  arrowLeft: ArrowLeft,
  arrowRight: ArrowRight,
  calendar: Calendar,
  clock: Clock,
  heart: Heart,
  star: Star,
  thumbsUp: ThumbsUp,
  thumbsDown: ThumbsDown,
  messageSquare: MessageSquare,
  copy: Copy,
  externalLink: ExternalLink,
  moreVertical: MoreVertical,
  save: Save,
  play: Play,
  pause: Pause,
  camera: Camera,
  image: Image,
  folder: Folder,
  upload: Upload,
  cloud: Cloud,
  sun: Sun,
  moon: Moon,
  monitor: Monitor,
  smartphone: Smartphone,
  globe: Globe,
  wifi: Wifi,
  power: Power,
  batteryCharging: BatteryCharging,
  signal: Signal,
  car: Car,
  mapPin: MapPin,
  building: Building,
  shoppingCart: ShoppingCart,
  gift: Gift,
  award: Award,
  trophy: Trophy,
  smile: Smile,
  frown: Frown,
  hand: Hand,
  eye: Eye,
  eyeOff: EyeOff,
  lock: Lock,
  unlock: Unlock,
  book: BookIcon,
  notebook: NotebookIcon,
  share2: Share2,
  filter: Filter,
  plus: Plus,
  minus: Minus,
  moreHorizontal: MoreHorizontal,
  edit3: Edit3,
  cancel: Cancel,
  skipForward: SkipForward,
  skipBack: SkipBack,
  volume2: Volume2,
  volumeX: VolumeX,
  mic: Mic,
  micOff: MicOff,
  video: Video,
  videoOff: VideoOff,
  file: File,
  folderOpen: FolderOpen,
  downloadCloud: DownloadCloud,
  cloudRain: CloudRain,
  cloudSnow: CloudSnow,
  laptop: Laptop,
  desktop: Desktop,
  server: Server,
  database: Database,
  bluetooth: Bluetooth,
  usb: Usb,
  hdmi: Hdmi,
  printer: Printer,
  rocket: Rocket,
  anchor: Anchor,
  compass: Compass,
  map: Map,
  location: Location,
  coffee: Coffee,
  pizza: Pizza,
  beer: Beer,
  wine: Wine,
  cart: CartIcon,
  giftIcon: GiftIcon,
  awardIcon: AwardIcon,
  trophyIcon: TrophyIcon,
  crown: Crown,
  // gem: Gem, // Removed as it's not available in lucide-react
  diamond: Diamond,
  robot: Robot,
  skull: Skull,
  bone: Bone,
  music: Music, // Added music icon
  helpCircle: HelpCircle, // Added helpCircle icon
  github: Github,
};
