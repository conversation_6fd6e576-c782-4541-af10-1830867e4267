{"name": "carbon-aware-llm-proxy", "version": "0.1.0", "private": true, "description": "A carbon-aware LLM proxy gateway with real-time emissions tracking", "installConfig": {"hoistingLimits": "workspaces"}, "workspaces": {"packages": ["packages/*"]}, "scripts": {"dev:backend": "yarn workspace @carbon-aware-llm/backend dev", "dev:frontend": "yarn workspace @carbon-aware-llm/frontend dev", "build": "yarn workspaces run build", "test": "yarn workspaces run test", "lint": "yarn workspaces run lint", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "prepare": "husky install", "deploy-runpod": "ts-node scripts/deploy-runpod.ts", "runpod": "npm run deploy-runpod --", "ts:check": "yarn workspaces foreach -ptA -- run tsc --noEmit", "ts:fix": "yarn workspaces foreach -ptA -- run tsc --noEmit || true", "prettier:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "prettier:fix": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "fix:all": "yarn ts:fix && yarn prettier:fix"}, "engines": {"node": ">=18.0.0"}, "packageManager": "yarn@4.9.2", "yarnPath": ".yarn/releases/yarn-3.6.1.cjs", "dependencies": {"commander": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "ts-node": "^10.9.0"}, "devDependencies": {"@types/commander": "^2.12.5", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.0", "prettier": "^3.0.0", "typescript": "^5.0.0"}, "author": "", "license": "MIT"}