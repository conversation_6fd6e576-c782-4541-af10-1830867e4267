name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

defaults:
  run:
    working-directory: .

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
          POSTGRES_DB: test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: '**/package-lock.json'
    
    - name: Install dependencies
      run: |
        npm ci
        cd packages/backend && npm ci
        cd ../frontend && npm ci
    
    - name: Run backend tests
      working-directory: packages/backend
      env:
        NODE_ENV: test
        DATABASE_URL: postgresql://test:test@localhost:5432/test
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test-secret
      run: |
        npm run test:ci
    
    - name: Run frontend tests
      working-directory: packages/frontend
      run: |
        npm run test:ci

  build:
    name: Build and Lint
    needs: test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: '**/package-lock.json'
    
    - name: Install dependencies
      run: |
        npm ci
        cd packages/backend && npm ci
        cd ../frontend && npm ci
    
    - name: Lint backend
      working-directory: packages/backend
      run: npm run lint
    
    - name: Lint frontend
      working-directory: packages/frontend
      run: npm run lint
    
    - name: Build backend
      working-directory: packages/backend
      run: npm run build
    
    - name: Build frontend
      working-directory: packages/frontend
      run: |
        npm run build
    
    - name: Check formatting
      run: npm run format:check

  docker:
    name: Build and Test Docker
    needs: [test, build]
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Cache Docker layers
      uses: actions/cache@v3
      with:
        path: /tmp/.buildx-cache
        key: ${{ runner.os }}-buildx-${{ github.sha }}
        restore-keys: |
          ${{ runner.os }}-buildx-
    
    - name: Login to Docker Hub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}
    
    - name: Build and push backend image
      uses: docker/build-push-action@v4
      with:
        context: .
        file: packages/backend/Dockerfile
        push: ${{ github.event_name != 'pull_request' }}
        tags: |
          ${{ secrets.DOCKERHUB_USERNAME }}/carbon-aware-backend:latest
          ${{ secrets.DOCKERHUB_USERNAME }}/carbon-aware-backend:${{ github.sha }}
        cache-from: type=local,src=/tmp/.buildx-cache
        cache-to: type=local,dest=/tmp/.buildx-cache-new
    
    - name: Build and push frontend image
      uses: docker/build-push-action@v4
      with:
        context: .
        file: packages/frontend/Dockerfile
        push: ${{ github.event_name != 'pull_request' }}
        tags: |
          ${{ secrets.DOCKERHUB_USERNAME }}/carbon-aware-frontend:latest
          ${{ secrets.DOCKERHUB_USERNAME }}/carbon-aware-frontend:${{ github.sha }}
        cache-from: type=local,src=/tmp/.buildx-cache
        cache-to: type=local,dest=/tmp/.buildx-cache-new
    
    - name: Move cache
      run: |
        rm -rf /tmp/.buildx-cache
        mv /tmp/.buildx-cache-new /tmp/.buildx-cache

  deploy:
    name: Deploy to Production
    needs: [test, build, docker]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    runs-on: ubuntu-latest
    steps:
    - name: Deploy to Vercel
      uses: amondnet/vercel-action@v20
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
        vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
        working-directory: packages/frontend
        vercel-args: '--prod'
    
    - name: Deploy Backend
      run: |
        # Add your backend deployment steps here
        # This could be deploying to a cloud provider like AWS, GCP, etc.
        echo "Deploying backend..."
      env:
        # Add any necessary environment variables here
        DEPLOY_TOKEN: ${{ secrets.DEPLOY_TOKEN }}
